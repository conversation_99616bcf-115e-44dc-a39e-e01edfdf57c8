/*
 * Copyright(c) RIB Software GmbH
 */

import { Translatable } from '@libs/platform/common';

/**
 * Detailed Exception Information.
 *
 * @group Dialogs
 */
// TODO: replace with some general IException type from platform-common?
export interface IDialogErrorInfo {
	/**
	 * Unique code for error.
	 */
	errorCode: number;

	/**
	 * Version of error.
	 */
	errorVersion: string;

	/**
	 * Short message about error.
	 */
	errorMessage: Translatable;

	/**
	 * Detailed description of error.
	 */
	errorDetail: Translatable;

	/**
	 * Detail stack trace.
	 */
	detailStackTrace: Translatable;

	/**
	 * Error detail method.
	 */
	detailMethod: string | null;

	/**
	 * Detail message;
	 */
	detailMessage?: Translatable;
}
