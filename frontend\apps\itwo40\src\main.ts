import { APP_INITIALIZER, enableProdMode, importProvidersFrom, inject, Injector, runInInjectionContext } from '@angular/core';
import { bootstrapApplication } from '@angular/platform-browser';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { CommonModule, HashLocationStrategy, LocationStrategy } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { environment } from './environments/environment';
import { AppComponent } from './app/app.component';
import { AppRoutingModule } from './app/app-routing.module';
import { preloadModules } from './app/model/module-management/preload-modules.model';

import { AuthInterceptorService, PLATFORM_AUTH_CONFIG, PlatformAuthenticationModule } from '@libs/platform/authentication';
import { AuthContextInterceptorService, PlatformConfigurationService, ServiceLocator } from '@libs/platform/common';
import { ErrorInterceptorService } from '@libs/ui/platform';
import { UiMainFrameModule } from '@libs/ui/main-frame';
import { UiGridModule } from '@libs/ui/grid';


/**
 * reading config.json via xhr call
 * @param config
 */
function configurationReaderXhr(config: PlatformConfigurationService) {
  return () => config.loadInitialConfiguration();
}

function authUserConfigFactory(configService: PlatformConfigurationService) {
  return configService.getOidcUserConfig();
}

if (environment.production) {
  enableProdMode();
}

bootstrapApplication(AppComponent, {
  providers: [
    importProvidersFrom(AppRoutingModule, CommonModule, HttpClientModule, PlatformAuthenticationModule, BrowserAnimationsModule, UiMainFrameModule, UiGridModule, ...preloadModules
    ),

    PlatformConfigurationService,
    { provide: APP_INITIALIZER, deps: [PlatformConfigurationService], multi: true, useFactory: configurationReaderXhr },
    { provide: PLATFORM_AUTH_CONFIG, deps: [PlatformConfigurationService], useFactory: authUserConfigFactory },
    {
      provide: APP_INITIALIZER,
      multi: true,
      deps: [Injector],
      useFactory: (injector: Injector) => () => {
        runInInjectionContext(injector, () => {
          ServiceLocator.injector = inject(Injector);
        });

      }
    },

    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptorService, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: AuthContextInterceptorService, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptorService, multi: true },
    { provide: LocationStrategy, useClass: HashLocationStrategy },

  ],
}).catch((err) => console.error(err));



