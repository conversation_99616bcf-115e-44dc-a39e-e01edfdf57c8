/*
 * Copyright(c) RIB Software GmbH
 */

import { IValidationService, ValidationInfo, ValidationResult } from '@libs/platform/data-access';
import { LazyInjectionToken } from '@libs/platform/common';
import { IPrcPackageEntity } from '../../entities/package';

/**
 * Package validation service interface
 */
export interface IPrcPackageValidationService extends IValidationService<IPrcPackageEntity> {
	validateDialogStructureFk(info: ValidationInfo<IPrcPackageEntity>, noSetConfigurationFk?: boolean): Promise<ValidationResult>;

	validateDialogConfigurationFk(info: ValidationInfo<IPrcPackageEntity>): Promise<ValidationResult>;

	validateDialogCode(info: ValidationInfo<IPrcPackageEntity>): Promise<ValidationResult>;
}

/**
 * The lazy injection token for package validation service
 */
export const PRC_PACKAGE_VALIDATION_SERVICE = new LazyInjectionToken<IPrcPackageValidationService>('prc-package-validation-service');
