import { Injectable, inject } from '@angular/core';
import { PlatformHttpService, PlatformLazyInjectorService, PlatformTranslateService, PropertyType } from '@libs/platform/common';
import { BasicsSharedAssetMasterLookupService, BasicsSharedNumberGenerationService, BasicsSharedProcurementConfigurationLookupService, BasicsSharedProcurementStructureLookupService, EntityRuntimeDataExtension, Rubric } from '@libs/basics/shared';
import { ProjectSharedLookupService } from '@libs/project/shared';
import { IPrcPackageEntity, IPrcPackageValidationService, PRC_PACKAGE_VALIDATION_SERVICE } from '@libs/procurement/interfaces';
import { createLookup, FieldType, FormRow, IFormConfig, StandardDialogButtonId, UiCommonFormDialogService } from '@libs/ui/common';
import { ICreatePackageOptions, ICreatePackageParameter } from '../boq/model/interfaces';
import { ValidationInfo, ValidationResult } from '@libs/platform/data-access';
import { get } from 'lodash';

@Injectable({
	providedIn: 'root',
})
export class ProcurementCommonCreatePackageService {
	private readonly http = inject(PlatformHttpService);
	private readonly structureLookupService = inject(BasicsSharedProcurementStructureLookupService);
	private readonly formDialogService = inject(UiCommonFormDialogService);
	private readonly projectLookupService = inject(ProjectSharedLookupService);
	private readonly prcConfigurationLookupService = inject(BasicsSharedProcurementConfigurationLookupService);
	private readonly lazyInjector = inject(PlatformLazyInjectorService);
	private readonly translationService = inject(PlatformTranslateService);
	private readonly numberGenerator = inject(BasicsSharedNumberGenerationService);
	private formRunTimeInfo?: EntityRuntimeDataExtension<ICreatePackageOptions>;
	private validationService?: IPrcPackageValidationService;

	public async createPackage(options: ICreatePackageOptions): Promise<IPrcPackageEntity | null> {
		this.formRunTimeInfo = new EntityRuntimeDataExtension();
		this.validationService = await this.lazyInjector.inject(PRC_PACKAGE_VALIDATION_SERVICE);

		await this.numberGenerator.getNumberGenerateConfig(Rubric.Package);
		await this.resolveStructureAndProjectData(options);
		await this.resolveConfigurationData(options);

		if (this.shouldAutoSave(options)) {
			return this.doCreatePackage(options);
		} else {
			const showAssetMaster = await this.http.get<boolean>('basics/common/systemoption/showassetmasterinprocurement');
			const result = await this.showCreatePackageDialog(options, showAssetMaster);
			if (result) {
				return this.doCreatePackage(result);
			}
		}
		return null;
	}

	private shouldAutoSave(options: ICreatePackageOptions): boolean {
		return !!(options.PrjProjectFk && options.StructureFk && options.IsAutoSave);
	}

	private async resolveStructureAndProjectData(createData: ICreatePackageOptions): Promise<void> {
		const structureList = await this.structureLookupService.getListAsync();
		const structure = structureList.find((s) => s.Id === createData.StructureFk);
		if (structure) {
			createData.Description = structure.DescriptionInfo?.Translated;
		}

		const projectList = await this.projectLookupService.getListAsync();
		const project = projectList.find((p) => p.Id === createData.PrjProjectFk);
		if (project) {
			createData.AssetMasterFk = project.AssetMasterFk ?? undefined;
		}
	}

	private async resolveConfigurationData(createData: ICreatePackageOptions): Promise<void> {
		const configList = await this.prcConfigurationLookupService.getListAsync();
		if (configList) {
			const packageConfig = configList.filter((item) => item.PrcConfigHeaderFk === createData.PrcConfigHeaderFk && item.RubricFk === Rubric.Package).sort((a, b) => a.Sorting - b.Sorting);
			if (Array.isArray(packageConfig) && packageConfig.length >= 1) {
				const item = packageConfig.find((i) => i.IsDefault);
				createData.ConfigurationFk = item ? item.Id : packageConfig[0].Id;
			}
		}

		if (!createData.ConfigurationFk) {
			const url = 'basics/procurementconfiguration/configuration/getByStructure';
			const params = {
				structureId: createData.StructureFk ?? '',
				rubricId: Rubric.Package,
			};
			createData.ConfigurationFk = await this.http.get<number>(url, { params });
		}
	}

	private async validateFormField(options: ICreatePackageOptions, row: FormRow<ICreatePackageOptions>, value?: PropertyType | null): Promise<void> {
		if (!this.validationService || !this.formRunTimeInfo) {
			return;
		}

		const formField = row.model as keyof ICreatePackageOptions;
		let packageFieldName = formField;
		let result: ValidationResult;

		switch (formField) {
			case 'PrjProjectFk':
				packageFieldName = 'ProjectFk';
				break;
		}

		if (formField === 'Code') {
			value = await this.checkCodeGeneration(options);
		}

		if (['Code', 'PrjProjectFk', 'StructureFk'].includes(formField) && !value) {
			const error = this.translationService.instant('cloud.common.entityRequired').text;
			result = new ValidationResult(error);
		} else {
			const validationInfo = new ValidationInfo<IPrcPackageEntity>(options as IPrcPackageEntity, value ?? undefined, packageFieldName);

			if (formField === 'StructureFk') {
				result = await this.validationService.validateDialogStructureFk(validationInfo);
				await this.checkCodeGeneration(options);
			} else if (formField === 'ConfigurationFk') {
				result = await this.validationService.validateDialogConfigurationFk(validationInfo);
				await this.checkCodeGeneration(options);
			} else if (formField === 'Code') {
				result = await this.validationService.validateDialogCode(validationInfo);
			} else {
				const validator = this.validationService?.getValidationFunc(packageFieldName);
				if (!validator) {
					return;
				}
				result = await validator(validationInfo);
			}
		}

		this.formRunTimeInfo.addInvalid({
			field: formField,
			result: result,
		});
	}

	private async showCreatePackageDialog(options: ICreatePackageOptions, showAssetMaster: boolean): Promise<ICreatePackageOptions | undefined> {
		const rows: FormRow<ICreatePackageOptions>[] = [
			{
				id: 'PrjProjectFk',
				model: 'PrjProjectFk',
				label: { key: 'cloud.common.entityProjectName' },
				type: FieldType.Lookup,
				lookupOptions: createLookup({
					dataServiceToken: ProjectSharedLookupService,
					descriptionMember: 'ProjectName',
					showDescription: true,
					serverSideFilter: {
						key: 'procurement-package-header-project-assetmasterfk-filter',
						execute: (context) => {
							return {
								IsLive: true,
								//AssetMasterFk: 0 //TODO - Seems it is not needed for boq creation, should reuse ProcurementPackageProjectAssetMasterFkFilterService if needed
							};
						},
					},
				}),
				readonly: options.IsProjectReadonly,
			},
			{
				id: 'StructureFk',
				model: 'StructureFk',
				label: { key: 'basics.common.entityPrcStructureFk' },
				type: FieldType.Lookup,
				lookupOptions: createLookup({
					dataServiceToken: BasicsSharedProcurementStructureLookupService,
					descriptionMember: 'DescriptionInfo.Translated',
					showDescription: true,
				}),
			},
			{
				id: 'ConfigurationFk',
				model: 'ConfigurationFk',
				label: { key: 'procurement.package.entityConfiguration' },
				type: FieldType.Lookup,
				lookupOptions: createLookup({
					dataServiceToken: BasicsSharedProcurementConfigurationLookupService,
					serverSideFilter: {
						key: 'procurement-package-configuration-filter',
						execute: (context) => {
							const filterStrings = [`RubricFk=${Rubric.Package} `];

							if (options.IsService) {
								filterStrings.push('IsService=true');
							}

							if (options.IsMaterial) {
								filterStrings.push('IsMaterial=true');
							}

							return filterStrings.join(' and ');
						},
					},
				}),
			},
			{
				id: 'Code',
				model: 'Code',
				label: { key: 'cloud.common.entityCode' },
				type: FieldType.Code,
			},
			{
				id: 'Description',
				model: 'Description',
				label: { key: 'cloud.common.entityDescription' },
				type: FieldType.Description,
			},
		];

		if (showAssetMaster) {
			rows.push({
				id: 'AssetMasterFk',
				model: 'AssetMasterFk',
				label: { key: 'procurement.package.entityAssetMaster' },
				type: FieldType.Lookup,
				lookupOptions: createLookup({
					dataServiceToken: BasicsSharedAssetMasterLookupService,
					showDescription: true,
					descriptionMember: 'DescriptionInfo.Translated',
					serverSideFilter: {
						key: 'basics-asset-master-dialog-filter',
						execute: async (context) => {
							if (context.entity?.PrjProjectFk) {
								const project = await this.projectLookupService.getItemByKeyAsync({ id: context.entity.PrjProjectFk });
								return {
									AssetMasterFk: project.AssetMasterFk,
								};
							}
							return;
						},
					},
				}),
			});
		}

		const formConfig: IFormConfig<ICreatePackageOptions> = {
			formId: 'procurement-common-create-package-form',
			showGrouping: false,
			addValidationAutomatically: false,
			rows: rows,
		};

		// trigger the mandatory validations
		rows
			.filter((e) => ['Code', 'PrjProjectFk', 'StructureFk'].includes(e.id))
			.forEach((row) => {
				this.validateFormField(options, row, get(options, row.model as string));
			});

		const result = await this.formDialogService.showDialog<ICreatePackageOptions>({
			id: 'createPrcPackage',
			headerText: { key: 'procurement.package.createDialogTitle' },
			width: '750px',
			formConfiguration: formConfig,
			entity: options,
			runtime: this.formRunTimeInfo,
			showCancelButton: true,
			onRowValueChanged: (data, changeInfo) => {
				const row = rows.find((e) => e.id === changeInfo.rowId);

				if (!row) {
					throw new Error('Form row is not found!');
				}

				this.validateFormField(options, row, changeInfo.newValue);
			},
		});

		if (result && result.closingButtonId === StandardDialogButtonId.Ok && result.value) {
			return options;
		}

		return undefined;
	}

	private async doCreatePackage(createData: ICreatePackageParameter): Promise<IPrcPackageEntity> {
		return this.http.post<IPrcPackageEntity>('procurement/package/package/create/createpackage', createData);
	}

	private async checkCodeGeneration(options: ICreatePackageOptions): Promise<string | undefined> {
		if (!options.ConfigurationFk) {
			this.setCodeReadonly(false);
			return options.Code;
		}

		const config = await this.prcConfigurationLookupService.getItemByKeyAsync({ id: options.ConfigurationFk });

		if (!config) {
			this.setCodeReadonly(false);
			return options.Code;
		}

		const hasConfig = this.numberGenerator.hasNumberGenerateConfig(config.RubricCategoryFk);

		if (hasConfig) {
			options.Code = this.numberGenerator.provideNumberDefaultText(config.RubricCategoryFk);
			this.setCodeReadonly(true);
		} else {
			this.setCodeReadonly(false);
		}

		return options.Code;
	}

	private setCodeReadonly(value: boolean) {
		this.formRunTimeInfo?.setEntityReadOnlyFields([
			{
				field: 'Code',
				readOnly: value,
			},
		]);
	}
}
