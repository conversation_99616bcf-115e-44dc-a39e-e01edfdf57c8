import { EntityDomainType, IEntitySchema } from '@libs/platform/data-access';
import { ValueOrType } from '@libs/platform/common';
import { ICosInsObjectTemplatePropertyEntity } from '@libs/constructionsystem/shared';

export const CONSTRUCTION_SYSTEM_MAIN_OBJECT_TEMPLATE_PROPERTY_SCHEME: ValueOrType<IEntitySchema<ICosInsObjectTemplatePropertyEntity>> = {
	schema: 'CosInsObjectTemplatePropertyDto',
	properties: {
		MdlPropertyKeyFk: { domain: EntityDomainType.Integer, mandatory: true },
		BasUomFk: { domain: EntityDomainType.Integer, mandatory: false },
		Formula: { domain: EntityDomainType.Description, mandatory: false },
	},
	additionalProperties: {
		Value: { domain: EntityDomainType.Description, mandatory: false }, //todo: dynamic field
	},
};
