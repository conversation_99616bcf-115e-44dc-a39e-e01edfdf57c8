/*
 * Copyright(c) RIB Software GmbH
 */
import { inject, Injectable } from '@angular/core';
import { BaseValidationService, IEntityRuntimeDataRegistry, IValidationFunctions, ValidationInfo, ValidationResult } from '@libs/platform/data-access';
import { IBasicsClerkEntity, IMtgAttendeeEntity } from '@libs/basics/interfaces';
import { BasicsSharedClerkLookupService } from '@libs/basics/shared';
import { BasicsMeetingAttendeeDataService } from '../basics-meeting-attendee-data.service';
import { BusinessPartnerLogicalValidatorFactoryService, BusinesspartnerSharedContactLookupService, BusinesspartnerSharedSubsidiaryLookupService } from '@libs/businesspartner/shared';
import { IContactLookupEntity } from '@libs/businesspartner/interfaces';

/**
 * Basics Meeting attendee validation service
 */
@Injectable({
	providedIn: 'root',
})
export class BasicsMeetingAttendeeValidationService extends BaseValidationService<IMtgAttendeeEntity> {
	private readonly dataService = inject(BasicsMeetingAttendeeDataService);
	private readonly clerkLookupService = inject(BasicsSharedClerkLookupService);
	private readonly contactLookupService = inject(BusinesspartnerSharedContactLookupService);
	private readonly subsidiaryLookupService = inject(BusinesspartnerSharedSubsidiaryLookupService);

	private readonly businessPartnerLogicalValidatorFactoryService = inject(BusinessPartnerLogicalValidatorFactoryService);
	private readonly bpValidator = this.businessPartnerLogicalValidatorFactoryService.create({
		dataService: this.dataService,
		businessPartnerField: 'BusinessPartnerFk',
		subsidiaryField: 'SubsidiaryFk',
		contactField: 'ContactFk',
		subsidiaryFromBpDialog: true,
		needLoadDefaultSupplier: false,
	});

	protected validateSubsidiaryFk = async (info: ValidationInfo<IMtgAttendeeEntity>) => this.bpValidator.subsidiaryValidator(info.entity, info.value as number);

	protected generateValidationFunctions(): IValidationFunctions<IMtgAttendeeEntity> {
		return {
			ClerkFk: this.validateClerkFk,
			BusinessPartnerFk: this.validateBusinessPartnerFk,
			SubsidiaryFk: this.validateSubsidiaryFk,
			ContactFk: this.validateContactFk,
		};
	}

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IMtgAttendeeEntity> {
		return this.dataService;
	}

	protected async validateClerkFk(info: ValidationInfo<IMtgAttendeeEntity>): Promise<ValidationResult> {
		const entity = info.entity;
		const value = info.value as number;

		if (value) {
			const clerkEntity = await this.clerkLookupService.getItemByKeyAsync({ id: value });
			this.setAdditionalColByClerk(entity, clerkEntity);
		} else {
			this.setAdditionalColValueNull(entity);
		}

		entity.ClerkFk = value;
		this.dataService.readonlyProcessor.process(entity);
		return this.createSuccessResult();
	}

	protected async validateBusinessPartnerFk(info: ValidationInfo<IMtgAttendeeEntity>): Promise<ValidationResult> {
		const entity = info.entity;
		const value = info.value as number;
		entity.SubsidiaryFk = null;
		entity.ContactFk = null;

		if (!value) {
			entity.BusinessPartnerFk = value;
			this.setAdditionalColValueNull(entity);
			this.dataService.readonlyProcessor.process(entity);
			return this.createSuccessResult();
		}

		const result = await this.bpValidator.businessPartnerValidator({
			entity: entity,
			value: value,
			notNeedLoadDefaultSubsidiary: false,
		});

		if (result.valid && entity.ContactFk) {
			const contact = await this.contactLookupService.getItemByKeyAsync({ id: entity.ContactFk });
			if (contact) {
				this.setAdditionalColByContact(entity, contact);
			}
		}

		entity.BusinessPartnerFk = value;
		this.dataService.readonlyProcessor.process(entity);
		return result;
	}

	protected async validateContactFk(info: ValidationInfo<IMtgAttendeeEntity>): Promise<ValidationResult> {
		const entity = info.entity;
		const value = info.value as number;

		if (!value) {
			this.setAdditionalColValueNull(entity);
			entity.ContactFk = null;
			this.dataService.readonlyProcessor.process(entity);
			return this.createSuccessResult();
		}

		entity.ClerkFk = null;
		const contact = await this.contactLookupService.getItemByKeyAsync({ id: value });
		if (contact) {
			this.setAdditionalColByContact(entity, contact);

			if (!entity.BusinessPartnerFk) {
				entity.BusinessPartnerFk = contact.BusinessPartnerFk;

				const mainSubsidiary = await this.getMainSubsidiary(value);
				entity.SubsidiaryFk = mainSubsidiary?.Id || null;
			}
		}

		entity.ContactFk = value;
		this.dataService.readonlyProcessor.process(entity);
		return this.createSuccessResult();
	}

	private async getMainSubsidiary(businessPartnerFk: number) {
		const mainSubsidiarys = await this.subsidiaryLookupService.getSearchListAsync({
			searchText: '',
			searchFields: [],
			filterString: `IsMainAddress=true and BusinessPartnerFk=${businessPartnerFk}`,
		});
		return mainSubsidiarys?.items?.[0] || null;
	}

	private setAdditionalColByClerk(entity: IMtgAttendeeEntity, clerk: IBasicsClerkEntity) {
		if (entity && clerk) {
			entity.Title = clerk.Title;
			entity.FirstName = clerk.FirstName;
			entity.FamilyName = clerk.FamilyName;
			entity.Department = clerk.Department;
			entity.Email = clerk.Email;
			entity.TelephoneNumberFk = clerk.TelephoneNumberFk;
			entity.TelephoneMobilFk = clerk.TelephoneMobilFk;
			entity.Role = null;
		}
	}

	private setAdditionalColByContact(entity: IMtgAttendeeEntity, contact: IContactLookupEntity) {
		if (entity && contact) {
			entity.Title = contact.Title;
			entity.FirstName = contact.FirstName;
			entity.FamilyName = contact.FamilyName;
			entity.Department = null;
			entity.Email = contact.Email;
			entity.TelephoneNumberFk = contact.TelephoneNumberFk;
			entity.TelephoneMobilFk = contact.TelephoneNumberMobilFk;
			entity.Role = contact.ContactRoleEntity?.DescriptionInfo?.Translated || null;
		}
	}

	private setAdditionalColValueNull(entity: IMtgAttendeeEntity) {
		if (entity) {
			entity.Title = null;
			entity.FirstName = null;
			entity.FamilyName = null;
			entity.Department = null;
			entity.Email = null;
			entity.TelephoneNumberFk = null;
			entity.TelephoneMobilFk = null;
			entity.Role = null;
		}
	}
}
