/*
 * Copyright(c) RIB Software GmbH
 */

import { IPrcPackageEntity } from '@libs/procurement/interfaces';

export interface ICreatePackageParameter extends Partial<IPrcPackageEntity> {
	/**
	 * gets and sets mainItemId
	 */
	MainItemId?: number;

	/**
	 * gets and sets ProjectFk
	 */
	PrjProjectFk: number;

	/**
	 * gets and sets PrcPackageTemplateFk
	 */
	PrcPackageTemplateFk?: number;

	IsAutoSave?: boolean;

	PrcPackageFk?: number | null;

	Budget?: number;

	/**
	 * 1 :create,2:update
	 */
	GenerateType?: string;

	Filter?: string;

	BasCurrencyFk?: number | null;

	PrcConfigHeaderFk?: number | null;

	Need2SaveSubEntitiesAutoCreated?: boolean;
}
