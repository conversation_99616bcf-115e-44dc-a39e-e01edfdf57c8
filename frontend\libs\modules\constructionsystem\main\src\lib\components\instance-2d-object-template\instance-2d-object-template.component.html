<div class="outerDiv">
    <div class="containerLayout splitArea1">
        <div class="toolbar subview-header" uiCommonCollapsableList [tools]="currentTools">
            <ng-container>
                <h2 class="title fix">{{ this.containerDefinition.title | platformTranslate }}</h2>
            </ng-container>
            <ng-template #elseNotDone>
                <h2 class="title fix">Loading...</h2>
            </ng-template>
            <div class="flex-box">
                <ui-common-toolbar [toolbarData]="currentTools"></ui-common-toolbar>
            </div>
        </div>

        <div class="contentaline subview-content">
            <ui-common-grid #gridHost uiCommonDragDropTarget [configuration]="config" (selectionChanged)="onSelectionChanged($event)" (valueChanged)="onValueChanged($event)"></ui-common-grid>
        </div>
    </div>
</div>