/*
 * Copyright(c) RIB Software GmbH
 */

import { Component, inject, Injector, OnInit, Type } from '@angular/core';
import { ContainerDefinition, ContainerInjectionInfo } from '@libs/ui/container-system';
import { EntityContainerInjectionTokens } from '@libs/ui/business-base';
import { ConstructionSystemSharedObjectTemplatePropertyLayoutService, ICosInsObjectTemplateEntity, ICosInsObjectTemplatePropertyEntity, ICosInstanceEntity } from '@libs/constructionsystem/shared';
import { ConstructionSystemMainObjectTemplateLayoutService } from '../../services/layouts/construction-system-main-object-template-layout.service';
import { ConstructionSystemMainObjectTemplateDataService } from '../../services/construction-system-main-object-template-data.service';
import { Instance2dObjectTemplateComponent } from '../instance-2d-object-template/instance-2d-object-template.component';
import { ConstructionSystemMainObjectTemplatePropertyDataService } from '../../services/construction-system-main-object-template-property-data.service';
import { NgComponentOutlet } from '@angular/common';
import { PlatformCommonModule, ServiceLocator } from '@libs/platform/common';
import { PlatformSchemaService } from '@libs/platform/data-access';
import { CONSTRUCTION_SYSTEM_MAIN_OBJECT_TEMPLATE_ENTITY_INFO } from '../../model/entity-info/construction-system-main-object-template-entity-info.model';
import { ConstructionSystemMainObjectTemplatePropertyValidationService } from '../../services/validations/construction-system-main-object-template-property-vaildation.service';
import { CONSTRUCTION_SYSTEM_MAIN_OBJECT_TEMPLATE_PROPERTY_ENTITY_INFO } from '../../model/entity-info/construction-system-main-object-template-property-entity-info.model';
import { ConstructionSystemMainInstanceDataService } from '../../services/construction-system-main-instance-data.service';
import { CONSTRUCTION_SYSTEM_MAIN_OBJECT_TEMPLATE_PROPERTY_SCHEME } from '../../model/entity-info/construction-system-main-object-2-template-property-schema.model';

@Component({
	selector: 'constructionsystem-main-instance-2d-object-dialog',
	templateUrl: './instance-2d-object-dialog.component.html',
	styleUrls: ['./instance-2d-object-dialog.component.scss'],
	standalone: true,
	imports: [NgComponentOutlet, PlatformCommonModule],
})
export class Instance2dObjectDialogComponent implements OnInit {
	private readonly platformSchemaService = ServiceLocator.injector.get(PlatformSchemaService<ICosInsObjectTemplateEntity>);
	private readonly constructionSystemSharedObjectTemplatePropertyLayoutService = ServiceLocator.injector.get(ConstructionSystemSharedObjectTemplatePropertyLayoutService<ICosInsObjectTemplatePropertyEntity>);
	private readonly newObjectTemplateDataService: ConstructionSystemMainObjectTemplateDataService;
	private readonly newObjectTemplatePropertyDataService: ConstructionSystemMainObjectTemplatePropertyDataService;
	public templateInjector!: Injector;
	public templatePropertyInjector!: Injector;
	private injector = inject(Injector);
	public objectTemplateGridComponent: Type<unknown> | null = null;
	public objectTemplatePropertyGridComponent: Type<unknown> | null = null;
	protected readonly currentInstance: ICosInstanceEntity | null;

	public constructor() {
		/**
		 * prepare new dataService avoid synchronization issues
		 */
		const instanceDataService = inject(ConstructionSystemMainInstanceDataService);
		this.currentInstance = instanceDataService.getSelectedEntity();
		this.newObjectTemplateDataService = new ConstructionSystemMainObjectTemplateDataService(instanceDataService);
		if (this.currentInstance) {
			this.newObjectTemplateDataService.loadSubEntities(instanceDataService.convertSelectedToIdentification(this.currentInstance)).then(() => {
				/// todo-joy: not a good solution, selectFirst seems will load all sub-entities,need to think a better solution to avoid unnecessary loading
				this.newObjectTemplateDataService.selectFirst();
			});
		}
		this.newObjectTemplatePropertyDataService = new ConstructionSystemMainObjectTemplatePropertyDataService(this.newObjectTemplateDataService);
	}

	public ngOnInit(): void {
		this.initObjectTemplateComponent();
		this.initObjectTemplatePropertyComponent();
	}

	/**
	 * Initializes the object template container component with the necessary providers and configurations.
	 * @private
	 */
	private async initObjectTemplateComponent() {
		const objectTemplateSchema = await this.platformSchemaService.getSchema({
			moduleSubModule: 'ConstructionSystem.Main',
			typeName: 'CosInsObjectTemplateDto',
		});
		this.templateInjector = Injector.create({
			parent: this.injector,
			providers: [
				{
					provide: new EntityContainerInjectionTokens<ICosInsObjectTemplateEntity>().layoutConfigurationToken,
					useFactory: () => new ConstructionSystemMainObjectTemplateLayoutService().generateLayout(),
				},
				{
					provide: new EntityContainerInjectionTokens<ICosInsObjectTemplateEntity>().dataServiceToken,
					useFactory: () => {
						return this.newObjectTemplateDataService;
					},
				},
				{
					provide: new EntityContainerInjectionTokens<ICosInsObjectTemplateEntity>().entitySchemaConfiguration,
					useFactory: () => objectTemplateSchema,
				},
				{
					provide: ContainerInjectionInfo.containerDefInjectionToken,
					useFactory: () => {
						const container = CONSTRUCTION_SYSTEM_MAIN_OBJECT_TEMPLATE_ENTITY_INFO.containers[1];
						return new ContainerDefinition({
							title: { key: 'constructionsystem.main.title2dObject', text: '2D Object' },
							uuid: container.uuid, /// very strange,if I use a new uuid, then the container is readonly status,why?
							containerType: container.containerType,
						});
					},
				},
			],
		});
		this.objectTemplateGridComponent = Instance2dObjectTemplateComponent;
	}

	/**
	 * Initializes the object template property container component with the necessary providers and configurations.
	 * @private
	 */
	private async initObjectTemplatePropertyComponent() {
		const layout = await this.constructionSystemSharedObjectTemplatePropertyLayoutService.generateLayout();
		this.templatePropertyInjector = Injector.create({
			parent: this.injector,
			providers: [
				{
					provide: new EntityContainerInjectionTokens<ICosInsObjectTemplatePropertyEntity>().layoutConfigurationToken,
					useFactory: () => layout,
				},
				{
					provide: new EntityContainerInjectionTokens<ICosInsObjectTemplatePropertyEntity>().dataServiceToken,
					useFactory: () => this.newObjectTemplatePropertyDataService,
				},
				{
					provide: new EntityContainerInjectionTokens<ICosInsObjectTemplatePropertyEntity>().entitySchemaConfiguration,
					useFactory: () => CONSTRUCTION_SYSTEM_MAIN_OBJECT_TEMPLATE_PROPERTY_SCHEME,
				},
				{
					provide: new EntityContainerInjectionTokens<ICosInsObjectTemplatePropertyEntity>().validationServiceToken,
					useExisting: ConstructionSystemMainObjectTemplatePropertyValidationService,
				},
				{
					provide: ContainerInjectionInfo.containerDefInjectionToken,
					useFactory: () => {
						const container = CONSTRUCTION_SYSTEM_MAIN_OBJECT_TEMPLATE_PROPERTY_ENTITY_INFO.containers[1];
						return new ContainerDefinition({
							title: { key: 'constructionsystem.main.title2dObjectProperty', text: '2D Object Property' },
							uuid: container.uuid, /// very strange,if I use a new uuid, then the container is readonly status
							containerType: container.containerType,
						});
					},
				},
			],
		});
		this.objectTemplatePropertyGridComponent = Instance2dObjectTemplateComponent;
	}
}
