/*
 * Copyright(c) RIB Software GmbH
 */

import { Component, inject } from '@angular/core';
import { GridContainerBaseComponent, IGridContainerLink, UiBusinessBaseEntityContainerMenulistHelperService } from '@libs/ui/business-base';
import { ConcreteMenuItem, GridComponent, IMenuItem, IMenuItemsList, IModuleNavigator, InsertPosition, IResizeArgs, MenuListContent, ModuleNavigatorIdentification, UiCommonModule } from '@libs/ui/common';
import { ContainerInjectionInfo } from '@libs/ui/container-system';
import { PlatformCommonModule, Translatable } from '@libs/platform/common';
import { NgComponentOutlet, NgForOf, NgIf } from '@angular/common';

@Component({
	selector: 'constructionsystem-main-instance-2d-object-template',
	templateUrl: './instance-2d-object-template.component.html',
	styleUrls: ['./instance-2d-object-template.component.scss'],
	standalone: true,
	providers: [
		{
			provide: ContainerInjectionInfo.uiAddOnsInjectionToken,
			useFactory: createUiAddOns,
		},
	],
	imports: [GridComponent, UiCommonModule, NgComponentOutlet, NgForOf, NgIf, PlatformCommonModule],
})
export class Instance2dObjectTemplateComponent<T extends object> extends GridContainerBaseComponent<T, IGridContainerLink<T>> {
	private readonly toolbarItems: ConcreteMenuItem[] = [];
	private readonly toolbarContent = new MenuListContent();

	public constructor() {
		super();
		this.containerLink = this.createGridContainerLink();
		this.generateGridColumns();
		const menuListHelperSvc = inject(UiBusinessBaseEntityContainerMenulistHelperService);
		this.toolbarItems = menuListHelperSvc.createListMenuItems(this.containerLink!, this.loadEntitySchema(), this.entityValidationService, this.layout);
		this.uiAddOns.toolbar.addItems(this.toolbarItems);
		this.attachToEntityServices();
	}

	public get currentTools(): IMenuItemsList | undefined {
		if (!this.toolbarContent.items || !this.toolbarContent.items.items || this.toolbarContent.items.items.length === 0) {
			this.toolbarContent.addItems(this.toolbarItems);
		}
		return this.toolbarContent.items;
	}
}

function createUiAddOns() {
	return {
		get busyOverlay() {
			return {
				info: '',
				visible: false,
				showInfo(info: Translatable): void {},
			};
		},
		get whiteboard() {
			return {
				info: '',
				visible: false,
				showInfo(info: Translatable): void {},
			};
		},
		toolbar: {
			addItems(newItems: IMenuItem | IMenuItem[], groupId?: string) {},
			addItemsAtId(newItems: IMenuItem | IMenuItem[], itemId: string, position?: InsertPosition): boolean {
				return false;
			},
			deleteItems(itemIds: string | string[]): boolean {
				return false;
			},
			clear() {},
			setVisibility(isVisible: boolean) {},
		},
		statusBar: {
			addItems(newItems: IMenuItem | IMenuItem[], groupId?: string) {},
			addItemsAtId(newItems: IMenuItem | IMenuItem[], itemId: string, position?: InsertPosition): boolean {
				return false;
			},
			deleteItems(itemIds: string | string[]): boolean {
				return false;
			},
			isVisible: false,
			clear() {},
			setVisibility(isVisible: boolean) {},
		},
		resizeMessenger: {
			register(handler: (args: IResizeArgs) => void) {},
			unregister(handler: (args: IResizeArgs) => void) {},
		},
		navigation: {
			addNavigator(navigator: IModuleNavigator | IModuleNavigator[]) {},
			removeNavigator(navigator: ModuleNavigatorIdentification) {},
			removeAllNavigator() {},
		},
	};
}
