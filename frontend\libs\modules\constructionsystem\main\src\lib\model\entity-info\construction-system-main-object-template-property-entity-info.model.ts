/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { ConstructionSystemSharedObjectTemplatePropertyLayoutService, ICosInsObjectTemplatePropertyEntity } from '@libs/constructionsystem/shared';
import { ConstructionSystemMainObjectTemplatePropertyDataService } from '../../services/construction-system-main-object-template-property-data.service';
import { ConstructionSystemMainObjectTemplatePropertyValidationService } from '../../services/validations/construction-system-main-object-template-property-vaildation.service';
import { CONSTRUCTION_SYSTEM_MAIN_OBJECT_TEMPLATE_PROPERTY_SCHEME } from './construction-system-main-object-2-template-property-schema.model';

export const CONSTRUCTION_SYSTEM_MAIN_OBJECT_TEMPLATE_PROPERTY_ENTITY_INFO: EntityInfo = EntityInfo.create<ICosInsObjectTemplatePropertyEntity>({
	grid: {
		title: { key: 'constructionsystem.master.2dObjectTemplatePropertyGridContainerTitle' },
	},
	form: {
		title: { key: 'constructionsystem.master.2dObjectTemplatePropertyFormContainerTitle' },
		containerUuid: 'fd0073b62d8142fe87072c49d31aac3c',
	},
	permissionUuid: '7e8e5c39d3314b87a23f8277ee0335e2',
	dtoSchemeId: { moduleSubModule: 'ConstructionSystem.Main', typeName: 'CosInsObjectTemplatePropertyDto' },
	dataService: (ctx) => ctx.injector.get(ConstructionSystemMainObjectTemplatePropertyDataService),
	validationService: (ctx) => ctx.injector.get(ConstructionSystemMainObjectTemplatePropertyValidationService),
	layoutConfiguration: (ctx) => ctx.injector.get(ConstructionSystemSharedObjectTemplatePropertyLayoutService<ICosInsObjectTemplatePropertyEntity>).generateLayout(),
	entitySchema: CONSTRUCTION_SYSTEM_MAIN_OBJECT_TEMPLATE_PROPERTY_SCHEME,
});
